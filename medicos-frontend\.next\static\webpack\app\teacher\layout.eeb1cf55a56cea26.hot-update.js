"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/teacher/layout",{

/***/ "(app-pages-browser)/./src/lib/constants/menuItems.ts":
/*!****************************************!*\
  !*** ./src/lib/constants/menuItems.ts ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   adminMenuItems: () => (/* binding */ adminMenuItems),\n/* harmony export */   collegeMenuItems: () => (/* binding */ collegeMenuItems),\n/* harmony export */   teacherMenuItems: () => (/* binding */ teacherMenuItems)\n/* harmony export */ });\n/* harmony import */ var _barrel_optimize_names_LayoutDashboard_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=LayoutDashboard!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _lib_constants_enums__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/constants/enums */ \"(app-pages-browser)/./src/lib/constants/enums.ts\");\n\n\nconst adminMenuItems = [\n    {\n        title: \"MENU\",\n        items: [\n            {\n                title: \"Dashboard\",\n                icon: null,\n                iconPath: \"/assets/icons/dashboard.svg\",\n                href: \"/admin\",\n                submenu: [\n                    {\n                        title: \"Overview\",\n                        href: \"/dashboard\"\n                    }\n                ],\n                roles: [\n                    _lib_constants_enums__WEBPACK_IMPORTED_MODULE_0__.UserRole.SUPER_ADMIN,\n                    _lib_constants_enums__WEBPACK_IMPORTED_MODULE_0__.UserRole.COLLEGE_ADMIN,\n                    _lib_constants_enums__WEBPACK_IMPORTED_MODULE_0__.UserRole.TEACHER\n                ]\n            },\n            {\n                title: \"Colleges\",\n                icon: null,\n                href: \"/admin/college\",\n                roles: [\n                    _lib_constants_enums__WEBPACK_IMPORTED_MODULE_0__.UserRole.SUPER_ADMIN\n                ]\n            },\n            {\n                title: \"Add College\",\n                icon: null,\n                href: \"/admin/add-college\",\n                roles: [\n                    _lib_constants_enums__WEBPACK_IMPORTED_MODULE_0__.UserRole.SUPER_ADMIN,\n                    _lib_constants_enums__WEBPACK_IMPORTED_MODULE_0__.UserRole.COLLEGE_ADMIN\n                ]\n            },\n            {\n                title: \"Question Bank\",\n                icon: null,\n                href: \"/admin/question-bank\",\n                roles: [\n                    _lib_constants_enums__WEBPACK_IMPORTED_MODULE_0__.UserRole.SUPER_ADMIN,\n                    _lib_constants_enums__WEBPACK_IMPORTED_MODULE_0__.UserRole.COLLEGE_ADMIN\n                ]\n            },\n            {\n                title: \"Question Bank\",\n                icon: null,\n                href: \"/admin/question-bank\",\n                roles: [\n                    _lib_constants_enums__WEBPACK_IMPORTED_MODULE_0__.UserRole.SUPER_ADMIN,\n                    _lib_constants_enums__WEBPACK_IMPORTED_MODULE_0__.UserRole.COLLEGE_ADMIN\n                ]\n            },\n            {\n                title: \"Add Question\",\n                icon: null,\n                href: \"/admin/add-question\",\n                roles: [\n                    _lib_constants_enums__WEBPACK_IMPORTED_MODULE_0__.UserRole.SUPER_ADMIN,\n                    _lib_constants_enums__WEBPACK_IMPORTED_MODULE_0__.UserRole.COLLEGE_ADMIN\n                ]\n            },\n            {\n                title: \"Analytics\",\n                icon: null,\n                href: \"/admin\",\n                badge: \"NEW\",\n                roles: [\n                    _lib_constants_enums__WEBPACK_IMPORTED_MODULE_0__.UserRole.SUPER_ADMIN,\n                    _lib_constants_enums__WEBPACK_IMPORTED_MODULE_0__.UserRole.COLLEGE_ADMIN\n                ]\n            }\n        ]\n    },\n    {\n        title: \"SUPPORT\",\n        items: [\n            {\n                title: \"Chat\",\n                iconPath: \"/assets/icons/chat.svg\",\n                icon: null,\n                href: \"/dashboard/chat\",\n                roles: [\n                    _lib_constants_enums__WEBPACK_IMPORTED_MODULE_0__.UserRole.SUPER_ADMIN,\n                    _lib_constants_enums__WEBPACK_IMPORTED_MODULE_0__.UserRole.COLLEGE_ADMIN,\n                    _lib_constants_enums__WEBPACK_IMPORTED_MODULE_0__.UserRole.TEACHER\n                ]\n            },\n            {\n                title: \"Email\",\n                iconPath: \"/assets/icons/email.svg\",\n                icon: null,\n                href: \"/dashboard/email\",\n                roles: [\n                    _lib_constants_enums__WEBPACK_IMPORTED_MODULE_0__.UserRole.SUPER_ADMIN,\n                    _lib_constants_enums__WEBPACK_IMPORTED_MODULE_0__.UserRole.COLLEGE_ADMIN,\n                    _lib_constants_enums__WEBPACK_IMPORTED_MODULE_0__.UserRole.TEACHER\n                ]\n            },\n            {\n                title: \"Invoice\",\n                iconPath: \"/assets/icons/invoice.svg\",\n                icon: null,\n                href: \"/dashboard/invoice\",\n                roles: [\n                    _lib_constants_enums__WEBPACK_IMPORTED_MODULE_0__.UserRole.SUPER_ADMIN,\n                    _lib_constants_enums__WEBPACK_IMPORTED_MODULE_0__.UserRole.COLLEGE_ADMIN\n                ]\n            }\n        ]\n    }\n];\nconst teacherMenuItems = [\n    {\n        title: \"MENU\",\n        items: [\n            {\n                title: \"Generate Questions\",\n                iconPath: \"/assets/icons/dashboard.svg\",\n                href: \"/teacher\",\n                submenu: [\n                    {\n                        title: \"Overview\",\n                        href: \"/dashboard\"\n                    }\n                ],\n                roles: [\n                    _lib_constants_enums__WEBPACK_IMPORTED_MODULE_0__.UserRole.SUPER_ADMIN,\n                    _lib_constants_enums__WEBPACK_IMPORTED_MODULE_0__.UserRole.COLLEGE_ADMIN,\n                    _lib_constants_enums__WEBPACK_IMPORTED_MODULE_0__.UserRole.TEACHER\n                ]\n            },\n            {\n                title: \"Settings\",\n                iconPath: \"\",\n                href: \"/teacher/settings\",\n                roles: [\n                    _lib_constants_enums__WEBPACK_IMPORTED_MODULE_0__.UserRole.SUPER_ADMIN\n                ]\n            },\n            {\n                title: \"Profile\",\n                iconPath: \"\",\n                href: \"/teacher/profile\",\n                roles: [\n                    _lib_constants_enums__WEBPACK_IMPORTED_MODULE_0__.UserRole.SUPER_ADMIN\n                ]\n            }\n        ]\n    },\n    {\n        title: \"SUPPORT\",\n        items: [\n            {\n                title: \"Chat\",\n                iconPath: \"/assets/icons/chat.svg\",\n                icon: null,\n                href: \"/dashboard/chat\",\n                roles: [\n                    _lib_constants_enums__WEBPACK_IMPORTED_MODULE_0__.UserRole.SUPER_ADMIN,\n                    _lib_constants_enums__WEBPACK_IMPORTED_MODULE_0__.UserRole.COLLEGE_ADMIN,\n                    _lib_constants_enums__WEBPACK_IMPORTED_MODULE_0__.UserRole.TEACHER\n                ]\n            },\n            {\n                title: \"Email\",\n                iconPath: \"/assets/icons/email.svg\",\n                icon: null,\n                href: \"/dashboard/email\",\n                roles: [\n                    _lib_constants_enums__WEBPACK_IMPORTED_MODULE_0__.UserRole.SUPER_ADMIN,\n                    _lib_constants_enums__WEBPACK_IMPORTED_MODULE_0__.UserRole.COLLEGE_ADMIN,\n                    _lib_constants_enums__WEBPACK_IMPORTED_MODULE_0__.UserRole.TEACHER\n                ]\n            },\n            {\n                title: \"Invoice\",\n                iconPath: \"/assets/icons/invoice.svg\",\n                icon: null,\n                href: \"/dashboard/invoice\",\n                roles: [\n                    _lib_constants_enums__WEBPACK_IMPORTED_MODULE_0__.UserRole.SUPER_ADMIN,\n                    _lib_constants_enums__WEBPACK_IMPORTED_MODULE_0__.UserRole.COLLEGE_ADMIN\n                ]\n            }\n        ]\n    }\n];\nconst collegeMenuItems = [\n    {\n        title: \"MENU\",\n        items: [\n            {\n                title: \"Dashboard\",\n                icon: _barrel_optimize_names_LayoutDashboard_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n                href: \"/college\",\n                roles: [\n                    _lib_constants_enums__WEBPACK_IMPORTED_MODULE_0__.UserRole.SUPER_ADMIN,\n                    _lib_constants_enums__WEBPACK_IMPORTED_MODULE_0__.UserRole.COLLEGE_ADMIN,\n                    _lib_constants_enums__WEBPACK_IMPORTED_MODULE_0__.UserRole.TEACHER\n                ]\n            },\n            {\n                title: \"Teacher management\",\n                icon: null,\n                href: \"/college/teachers-list\",\n                roles: [\n                    _lib_constants_enums__WEBPACK_IMPORTED_MODULE_0__.UserRole.SUPER_ADMIN\n                ]\n            }\n        ]\n    },\n    {\n        title: \"SUPPORT\",\n        items: [\n            {\n                title: \"Chat\",\n                iconPath: \"/assets/icons/chat.svg\",\n                icon: null,\n                href: \"/dashboard/chat\",\n                roles: [\n                    _lib_constants_enums__WEBPACK_IMPORTED_MODULE_0__.UserRole.SUPER_ADMIN,\n                    _lib_constants_enums__WEBPACK_IMPORTED_MODULE_0__.UserRole.COLLEGE_ADMIN,\n                    _lib_constants_enums__WEBPACK_IMPORTED_MODULE_0__.UserRole.TEACHER\n                ]\n            },\n            {\n                title: \"Email\",\n                iconPath: \"/assets/icons/email.svg\",\n                icon: null,\n                href: \"/dashboard/email\",\n                roles: [\n                    _lib_constants_enums__WEBPACK_IMPORTED_MODULE_0__.UserRole.SUPER_ADMIN,\n                    _lib_constants_enums__WEBPACK_IMPORTED_MODULE_0__.UserRole.COLLEGE_ADMIN,\n                    _lib_constants_enums__WEBPACK_IMPORTED_MODULE_0__.UserRole.TEACHER\n                ]\n            },\n            {\n                title: \"Invoice\",\n                iconPath: \"/assets/icons/invoice.svg\",\n                icon: null,\n                href: \"/dashboard/invoice\",\n                roles: [\n                    _lib_constants_enums__WEBPACK_IMPORTED_MODULE_0__.UserRole.SUPER_ADMIN,\n                    _lib_constants_enums__WEBPACK_IMPORTED_MODULE_0__.UserRole.COLLEGE_ADMIN\n                ]\n            }\n        ]\n    }\n];\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/constants/menuItems.ts\n"));

/***/ })

});