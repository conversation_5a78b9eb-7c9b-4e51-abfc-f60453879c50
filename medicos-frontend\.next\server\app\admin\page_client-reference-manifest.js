globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/admin/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./src/components/ui/toaster.tsx":{"*":{"id":"(ssr)/./src/components/ui/toaster.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/lib/AuthContext.tsx":{"*":{"id":"(ssr)/./src/lib/AuthContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/lib/ReactQueryProvider.tsx":{"*":{"id":"(ssr)/./src/lib/ReactQueryProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/react-slot/dist/index.mjs":{"*":{"id":"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/image-component.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/landing-page/navbar.tsx":{"*":{"id":"(ssr)/./src/components/landing-page/navbar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/landing-page/TestimonialsContact.tsx":{"*":{"id":"(ssr)/./src/components/landing-page/TestimonialsContact.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/login/page.tsx":{"*":{"id":"(ssr)/./src/app/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/layout.tsx":{"*":{"id":"(ssr)/./src/app/admin/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/page.tsx":{"*":{"id":"(ssr)/./src/app/admin/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/add-question/page.tsx":{"*":{"id":"(ssr)/./src/app/admin/add-question/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/question-bank/page.tsx":{"*":{"id":"(ssr)/./src/app/admin/question-bank/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/edit-question/[id]/page.tsx":{"*":{"id":"(ssr)/./src/app/admin/edit-question/[id]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/teacher/layout.tsx":{"*":{"id":"(ssr)/./src/app/teacher/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/teacher/page.tsx":{"*":{"id":"(ssr)/./src/app/teacher/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/teacher/settings/page.tsx":{"*":{"id":"(ssr)/./src/app/teacher/settings/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/teacher/profile/page.tsx":{"*":{"id":"(ssr)/./src/app/teacher/profile/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/add-subjectandtopic/page.tsx":{"*":{"id":"(ssr)/./src/app/admin/add-subjectandtopic/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/add-college/page.tsx":{"*":{"id":"(ssr)/./src/app/admin/add-college/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Outfit\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"100\",\"200\",\"300\",\"400\",\"500\",\"600\",\"700\",\"800\",\"900\"],\"display\":\"swap\",\"variable\":\"--font-outfit\"}],\"variableName\":\"outfit\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Outfit\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"100\",\"200\",\"300\",\"400\",\"500\",\"600\",\"700\",\"800\",\"900\"],\"display\":\"swap\",\"variable\":\"--font-outfit\"}],\"variableName\":\"outfit\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\toaster.tsx":{"id":"(app-pages-browser)/./src/components/ui/toaster.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\AuthContext.tsx":{"id":"(app-pages-browser)/./src/lib/AuthContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\ReactQueryProvider.tsx":{"id":"(app-pages-browser)/./src/lib/ReactQueryProvider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\node_modules\\@radix-ui\\react-slot\\dist\\index.mjs":{"id":"(app-pages-browser)/./node_modules/@radix-ui/react-slot/dist/index.mjs","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\node_modules\\next\\dist\\client\\app-dir\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\node_modules\\next\\dist\\esm\\client\\app-dir\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\node_modules\\next\\dist\\client\\image-component.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\node_modules\\next\\dist\\esm\\client\\image-component.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\components\\\\landing-page\\\\TransformingEducation.tsx\",\"import\":\"Nunito\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":\"800\",\"variable\":\"--font-nunito\"}],\"variableName\":\"nunito\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\components\\\\landing-page\\\\TransformingEducation.tsx\",\"import\":\"Nunito\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":\"800\",\"variable\":\"--font-nunito\"}],\"variableName\":\"nunito\"}","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\components\\\\landing-page\\\\TransformingEducation.tsx\",\"import\":\"Manrope\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":\"500\",\"variable\":\"--font-manrope\"}],\"variableName\":\"manrope\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\components\\\\landing-page\\\\TransformingEducation.tsx\",\"import\":\"Manrope\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":\"500\",\"variable\":\"--font-manrope\"}],\"variableName\":\"manrope\"}","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\landing-page\\navbar.tsx":{"id":"(app-pages-browser)/./src/components/landing-page/navbar.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\landing-page\\TestimonialsContact.tsx":{"id":"(app-pages-browser)/./src/components/landing-page/TestimonialsContact.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\login\\page.tsx":{"id":"(app-pages-browser)/./src/app/login/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\layout.tsx":{"id":"(app-pages-browser)/./src/app/admin/layout.tsx","name":"*","chunks":["app/admin/layout","static/chunks/app/admin/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\page.tsx":{"id":"(app-pages-browser)/./src/app/admin/page.tsx","name":"*","chunks":["app/admin/page","static/chunks/app/admin/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\add-question\\page.tsx":{"id":"(app-pages-browser)/./src/app/admin/add-question/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\question-bank\\page.tsx":{"id":"(app-pages-browser)/./src/app/admin/question-bank/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\edit-question\\[id]\\page.tsx":{"id":"(app-pages-browser)/./src/app/admin/edit-question/[id]/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\teacher\\layout.tsx":{"id":"(app-pages-browser)/./src/app/teacher/layout.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\teacher\\page.tsx":{"id":"(app-pages-browser)/./src/app/teacher/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\teacher\\settings\\page.tsx":{"id":"(app-pages-browser)/./src/app/teacher/settings/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\teacher\\profile\\page.tsx":{"id":"(app-pages-browser)/./src/app/teacher/profile/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\add-subjectandtopic\\page.tsx":{"id":"(app-pages-browser)/./src/app/admin/add-subjectandtopic/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\add-college\\page.tsx":{"id":"(app-pages-browser)/./src/app/admin/add-college/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\":[],"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\loading":[],"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\page":[{"inlined":false,"path":"static/css/app/page.css"}],"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\layout":[],"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\loading":[],"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\page":[]},"rscModuleMapping":{"(app-pages-browser)/./src/app/globals.css":{"*":{"id":"(rsc)/./src/app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/toaster.tsx":{"*":{"id":"(rsc)/./src/components/ui/toaster.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/lib/AuthContext.tsx":{"*":{"id":"(rsc)/./src/lib/AuthContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/lib/ReactQueryProvider.tsx":{"*":{"id":"(rsc)/./src/lib/ReactQueryProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/react-slot/dist/index.mjs":{"*":{"id":"(rsc)/./node_modules/@radix-ui/react-slot/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/image-component.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/landing-page/navbar.tsx":{"*":{"id":"(rsc)/./src/components/landing-page/navbar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/landing-page/TestimonialsContact.tsx":{"*":{"id":"(rsc)/./src/components/landing-page/TestimonialsContact.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/login/page.tsx":{"*":{"id":"(rsc)/./src/app/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/layout.tsx":{"*":{"id":"(rsc)/./src/app/admin/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/page.tsx":{"*":{"id":"(rsc)/./src/app/admin/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/add-question/page.tsx":{"*":{"id":"(rsc)/./src/app/admin/add-question/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/question-bank/page.tsx":{"*":{"id":"(rsc)/./src/app/admin/question-bank/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/edit-question/[id]/page.tsx":{"*":{"id":"(rsc)/./src/app/admin/edit-question/[id]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/teacher/layout.tsx":{"*":{"id":"(rsc)/./src/app/teacher/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/teacher/page.tsx":{"*":{"id":"(rsc)/./src/app/teacher/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/teacher/settings/page.tsx":{"*":{"id":"(rsc)/./src/app/teacher/settings/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/teacher/profile/page.tsx":{"*":{"id":"(rsc)/./src/app/teacher/profile/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/add-subjectandtopic/page.tsx":{"*":{"id":"(rsc)/./src/app/admin/add-subjectandtopic/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/add-college/page.tsx":{"*":{"id":"(rsc)/./src/app/admin/add-college/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}