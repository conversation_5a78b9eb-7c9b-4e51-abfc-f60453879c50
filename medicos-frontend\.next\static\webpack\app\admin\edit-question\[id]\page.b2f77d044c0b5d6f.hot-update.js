"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/edit-question/[id]/page",{

/***/ "(app-pages-browser)/./src/utils/imageUtils.ts":
/*!*********************************!*\
  !*** ./src/utils/imageUtils.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ensureDataUrl: () => (/* binding */ ensureDataUrl),\n/* harmony export */   extractImagesFromText: () => (/* binding */ extractImagesFromText),\n/* harmony export */   isBase64Image: () => (/* binding */ isBase64Image),\n/* harmony export */   validateBase64ImageSrc: () => (/* binding */ validateBase64ImageSrc)\n/* harmony export */ });\n/**\n * Utility functions for handling images, including base64 detection and conversion\n */ /**\n * Checks if a string is a base64 encoded image\n */ function isBase64Image(str) {\n    if (!str || typeof str !== 'string') return false;\n    // Check for data URL format\n    const dataUrlPattern = /^data:image\\/(png|jpg|jpeg|gif|webp|svg\\+xml);base64,/i;\n    if (dataUrlPattern.test(str)) return true;\n    // Check for raw base64 (without data URL prefix)\n    // Base64 strings are typically long and contain only valid base64 characters\n    const base64Pattern = /^[A-Za-z0-9+/]*={0,2}$/;\n    return str.length > 100 && base64Pattern.test(str);\n}\n/**\n * Converts a base64 string to a data URL if it's not already one\n */ function ensureDataUrl(base64String) {\n    if (!base64String) return '';\n    // If it's already a data URL, return as is\n    if (base64String.startsWith('data:image/')) {\n        return base64String;\n    }\n    // If it's raw base64, add the data URL prefix\n    // Default to PNG if we can't determine the format\n    return \"data:image/png;base64,\".concat(base64String);\n}\n/**\n * Extracts and processes images from text content\n * Returns an object with cleaned text and extracted images\n */ function extractImagesFromText(text) {\n    if (!text) return {\n        cleanText: text,\n        images: []\n    };\n    const images = [];\n    let cleanText = text;\n    // Look for base64 patterns in the text\n    const base64Patterns = [\n        // Data URL pattern\n        /data:image\\/[^;]+;base64,[A-Za-z0-9+/]+=*/g,\n        // Raw base64 pattern (more conservative - must be quite long)\n        /\\b[A-Za-z0-9+/]{200,}={0,2}\\b/g\n    ];\n    base64Patterns.forEach((pattern, patternIndex)=>{\n        const matches = text.match(pattern);\n        if (matches) {\n            matches.forEach((match, matchIndex)=>{\n                if (isBase64Image(match)) {\n                    const imageId = \"extracted-image-\".concat(patternIndex, \"-\").concat(matchIndex);\n                    const imageSrc = ensureDataUrl(match);\n                    images.push({\n                        id: imageId,\n                        src: imageSrc,\n                        alt: \"Extracted image \".concat(images.length + 1)\n                    });\n                    // Remove the base64 string from the text completely\n                    cleanText = cleanText.replace(match, '');\n                }\n            });\n        }\n    });\n    // Clean up any extra whitespace left after removing base64 strings\n    cleanText = cleanText.replace(/\\s+/g, ' ').trim();\n    return {\n        cleanText,\n        images\n    };\n}\n/**\n * Validates and sanitizes base64 image source\n */ function validateBase64ImageSrc(src) {\n    if (!src || !isBase64Image(src)) return null;\n    try {\n        const dataUrl = ensureDataUrl(src);\n        // Additional validation could be added here\n        return dataUrl;\n    } catch (error) {\n        console.warn('Invalid base64 image source:', error);\n        return null;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/imageUtils.ts\n"));

/***/ })

});