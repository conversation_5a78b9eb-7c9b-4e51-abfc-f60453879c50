"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/add-question/page",{

/***/ "(app-pages-browser)/./src/app/admin/add-question/page.tsx":
/*!*********************************************!*\
  !*** ./src/app/admin/add-question/page.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Breadcrumb__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Breadcrumb */ \"(app-pages-browser)/./src/components/Breadcrumb.tsx\");\n/* harmony import */ var _components_admin_add_question_form__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/admin/add-question-form */ \"(app-pages-browser)/./src/components/admin/add-question-form.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst AddQuestionPage = ()=>{\n    // Handle data changes from the SubjectTopicManager\n    const handleDataChange = (subjects, topics)=>{\n        console.log(\"Data updated:\", {\n            subjects,\n            topics\n        });\n    // You can save to localStorage, etc. if needed\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"min-h-screen bg-gray-50 py-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-xl font-semibold text-black\",\n                            children: \"Add Question\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\admin\\\\add-question\\\\page.tsx\",\n                            lineNumber: 20,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Breadcrumb__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            items: [\n                                {\n                                    label: 'Home',\n                                    href: '/'\n                                },\n                                {\n                                    label: '...',\n                                    href: '#'\n                                },\n                                {\n                                    label: 'Add Question'\n                                }\n                            ],\n                            className: \"text-sm mt-1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\admin\\\\add-question\\\\page.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\admin\\\\add-question\\\\page.tsx\",\n                    lineNumber: 19,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto py-10\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_add_question_form__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\admin\\\\add-question\\\\page.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\admin\\\\add-question\\\\page.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\admin\\\\add-question\\\\page.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\admin\\\\add-question\\\\page.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, undefined);\n};\n_c = AddQuestionPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AddQuestionPage);\nvar _c;\n$RefreshReg$(_c, \"AddQuestionPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/add-question/page.tsx\n"));

/***/ })

});